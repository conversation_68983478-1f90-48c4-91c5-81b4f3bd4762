"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { SearchBar, SearchFilters } from "./search-bar"
import {
  GlassCard,
  GradientButton,
  IconContainer,
  ModernBadge,
  AnimatedBackground,
  SectionContainer
} from "@/components/ui/modern-components"
import {
  MapPin,
  Star,
  Users,
  Shield,
  Zap,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Sparkles
} from "lucide-react"

interface HeroSectionProps {
  onSearch: (query: string, filters: SearchFilters) => void
}



const stats = [
  {
    icon: Users,
    value: "10,000+",
    label: "Pengguna Aktif"
  },
  {
    icon: MapPin,
    value: "500+",
    label: "Kost Terdaftar"
  },
  {
    icon: Star,
    value: "4.8",
    label: "Rating Rata-rata"
  },
  {
    icon: Shield,
    value: "100%",
    label: "Terverifikasi"
  }
]

const features = [
  {
    icon: Zap,
    title: "Preview Dinamis",
    description: "Lihat detail kost dengan preview interaktif dan carousel gambar yang memukau"
  },
  {
    icon: TrendingUp,
    title: "Perbandingan <PERSON>dah",
    description: "Bandingkan hingga 3 kost sekaligus dengan tabel perbandingan yang detail dan informatif"
  },
  {
    icon: CheckCircle,
    title: "Terverifikasi",
    description: "Semua kost telah diverifikasi untuk memastikan kualitas dan keamanan terbaik"
  }
]

const popularLocations = [
  "Jakarta Selatan",
  "Bandung",
  "Yogyakarta",
  "Surabaya",
  "Malang",
  "Semarang"
]

export function HeroSection({ onSearch }: HeroSectionProps) {
  const handleLocationSearch = (location: string) => {
    const filters: SearchFilters = {
      location,
      type: "semua",
      priceRange: [500000, 5000000],
      facilities: [],
      sortBy: "relevance"
    }
    onSearch("", filters)
  }

  return (
    <section className="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Modern Background Elements */}
      <AnimatedBackground />
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/90 via-blue-900/20 to-emerald-900/30" />

      <div className="relative container mx-auto px-4 py-16 sm:py-20 lg:py-32">
        {/* Hero Content */}
        <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center min-h-[80vh]">
          {/* Left Content */}
          <div className="lg:col-span-7 space-y-6 lg:space-y-8 text-center lg:text-left">
            <div className="space-y-4 lg:space-y-6">
              <ModernBadge variant="gradient" className="inline-flex">
                <Sparkles className="h-4 w-4" />
                Platform Pencarian Kost Terdepan
              </ModernBadge>

              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight">
                Temukan Kost
                <br />
                <span className="bg-gradient-to-r from-blue-400 to-emerald-400 bg-clip-text text-transparent">
                  Impian Anda
                </span>
              </h1>

              <p className="text-lg sm:text-xl md:text-2xl text-slate-300 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                Platform inovatif dengan preview dinamis dan fitur perbandingan interaktif
                untuk membantu Anda menemukan tempat tinggal yang sempurna.
              </p>
            </div>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto lg:mx-0">
              <SearchBar
                onSearch={onSearch}
                placeholder="Cari berdasarkan lokasi, nama kost, atau fasilitas..."
                className="bg-white/10 backdrop-blur-md rounded-2xl p-2 border border-white/20 shadow-2xl"
              />
            </div>

            {/* Popular Locations */}
            <div className="space-y-3 lg:space-y-4">
              <p className="text-slate-400 text-sm font-medium">Lokasi Populer:</p>
              <div className="flex flex-wrap justify-center lg:justify-start gap-2 lg:gap-3">
                {popularLocations.map((location, index) => (
                  <Button
                    key={location}
                    variant="outline"
                    size="sm"
                    className="bg-white/5 border-white/20 text-white hover:bg-white/10 hover:border-white/30 transition-all duration-300 backdrop-blur-sm text-xs sm:text-sm"
                    onClick={() => handleLocationSearch(location)}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <MapPin className="h-3 w-3 mr-1 sm:mr-2" />
                    {location}
                  </Button>
                ))}
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 lg:gap-4 pt-2 lg:pt-4">
              <GradientButton
                size="lg"
                className="w-full sm:w-auto"
                onClick={() => {
                  document.getElementById('kost-listings')?.scrollIntoView({
                    behavior: 'smooth'
                  })
                }}
              >
                Jelajahi Kost Sekarang
                <ArrowRight className="h-4 w-4 lg:h-5 lg:w-5" />
              </GradientButton>
              <GradientButton
                size="lg"
                variant="outline"
                className="w-full sm:w-auto"
              >
                Pelajari Lebih Lanjut
              </GradientButton>
            </div>
          </div>

          {/* Right Content - Stats */}
          <div className="lg:col-span-5 mt-8 lg:mt-0">
            <div className="grid grid-cols-2 gap-4 lg:gap-6">
              {stats.map((stat, index) => (
                <GlassCard key={index} delay={index * 200} className="p-4 lg:p-6">
                  <div className="flex flex-col items-center text-center space-y-2 lg:space-y-3">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-emerald-400 rounded-full blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300" />
                      <IconContainer icon={stat.icon} size="md" variant="gradient" />
                    </div>
                    <div className="space-y-1">
                      <div className="text-xl sm:text-2xl md:text-3xl font-bold text-white">
                        {stat.value}
                      </div>
                      <div className="text-xs sm:text-sm text-slate-300 font-medium">
                        {stat.label}
                      </div>
                    </div>
                  </div>
                </GlassCard>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <SectionContainer background="gradient">
        <div className="container mx-auto px-4 py-16 sm:py-20">
          <div className="text-center mb-12 sm:mb-16">
            <ModernBadge variant="gradient" className="mb-4 sm:mb-6 inline-flex">
              <CheckCircle className="h-4 w-4" />
              Mengapa Memilih KostHub?
            </ModernBadge>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6">
              Fitur Unggulan Kami
            </h2>
            <p className="text-slate-300 text-lg sm:text-xl max-w-3xl mx-auto leading-relaxed px-4">
              Teknologi terdepan yang memudahkan pencarian kost impian Anda dengan pengalaman yang tak terlupakan
            </p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 max-w-6xl mx-auto">
            {features.map((feature, index) => (
              <GlassCard key={index} delay={index * 200} className="p-6 sm:p-8 text-center sm:text-left">
                <div className="space-y-4">
                  <div className="relative flex justify-center sm:justify-start">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-emerald-400 rounded-2xl blur-lg opacity-20 group-hover:opacity-40 transition-opacity duration-300" />
                    <IconContainer
                      icon={feature.icon}
                      size="lg"
                      variant="glass"
                      className="relative"
                    />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg sm:text-xl font-semibold text-white">
                      {feature.title}
                    </h3>
                    <p className="text-slate-300 leading-relaxed text-sm sm:text-base">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </GlassCard>
            ))}
          </div>
        </div>
      </SectionContainer>
    </section>
  )
}

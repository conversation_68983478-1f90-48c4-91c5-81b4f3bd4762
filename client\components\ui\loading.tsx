"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { spinnerVariants } from "@/utils/animations"

const loadingVariants = cva(
  "flex items-center justify-center",
  {
    variants: {
      variant: {
        spinner: "",
        skeleton: "animate-pulse bg-muted rounded",
        dots: "space-x-1",
      },
      size: {
        sm: "h-4 w-4",
        md: "h-6 w-6",
        lg: "h-8 w-8",
      },
    },
    defaultVariants: {
      variant: "spinner",
      size: "md",
    },
  }
)

interface LoadingProps extends VariantProps<typeof loadingVariants> {
  text?: string;
  className?: string;
}

// Spinner Loading Component
function SpinnerLoading({ size, className }: { size?: "sm" | "md" | "lg"; className?: string }) {
  const sizeClasses = {
    sm: "h-4 w-4 border-2",
    md: "h-6 w-6 border-2",
    lg: "h-8 w-8 border-3",
  };

  return (
    <motion.div
      className={cn(
        "rounded-full border-current border-t-transparent",
        sizeClasses[size || "md"],
        className
      )}
      variants={spinnerVariants}
      animate="animate"
      aria-hidden="true"
    />
  );
}

// Skeleton Loading Component
function SkeletonLoading({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-muted", className)}
      {...props}
    />
  );
}

// Dots Loading Component
function DotsLoading({ size, className }: { size?: "sm" | "md" | "lg"; className?: string }) {
  const dotSizes = {
    sm: "h-1 w-1",
    md: "h-2 w-2",
    lg: "h-3 w-3",
  };

  const dotSize = dotSizes[size || "md"];

  return (
    <div className={cn("flex space-x-1", className)}>
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={cn("bg-current rounded-full", dotSize)}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7],
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: index * 0.2,
          }}
          aria-hidden="true"
        />
      ))}
    </div>
  );
}

// Main Loading Component
export function Loading({ variant = "spinner", size = "md", text, className }: LoadingProps) {
  const renderLoading = () => {
    switch (variant) {
      case "skeleton":
        return <SkeletonLoading className={cn(loadingVariants({ size }), className)} />;
      case "dots":
        return <DotsLoading size={size} className={className} />;
      case "spinner":
      default:
        return <SpinnerLoading size={size} className={className} />;
    }
  };

  return (
    <div className={cn("flex flex-col items-center justify-center gap-2", className)}>
      {renderLoading()}
      {text && (
        <p className="text-sm text-muted-foreground animate-pulse" aria-live="polite">
          {text}
        </p>
      )}
    </div>
  );
}

// Individual loading components for specific use cases
export function LoadingSpinner({ size = "md", className }: { size?: "sm" | "md" | "lg"; className?: string }) {
  return <SpinnerLoading size={size} className={className} />;
}

export function LoadingSkeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return <SkeletonLoading className={className} {...props} />;
}

export function LoadingDots({ size = "md", className }: { size?: "sm" | "md" | "lg"; className?: string }) {
  return <DotsLoading size={size} className={className} />;
}

// Skeleton variants for different content types
export function SkeletonText({ lines = 3, className }: { lines?: number; className?: string }) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <SkeletonLoading
          key={index}
          className={cn(
            "h-4",
            index === lines - 1 ? "w-3/4" : "w-full" // Last line is shorter
          )}
        />
      ))}
    </div>
  );
}

export function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-4 p-4", className)}>
      <SkeletonLoading className="h-48 w-full" />
      <div className="space-y-2">
        <SkeletonLoading className="h-4 w-3/4" />
        <SkeletonLoading className="h-4 w-1/2" />
      </div>
    </div>
  );
}

export function SkeletonAvatar({ size = "md", className }: { size?: "sm" | "md" | "lg"; className?: string }) {
  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-10 w-10",
    lg: "h-12 w-12",
  };

  return (
    <SkeletonLoading
      className={cn("rounded-full", sizeClasses[size], className)}
    />
  );
}

// Loading overlay for full-screen loading states
export function LoadingOverlay({ 
  isVisible, 
  text = "Loading...", 
  className 
}: { 
  isVisible: boolean; 
  text?: string; 
  className?: string; 
}) {
  if (!isVisible) return null;

  return (
    <motion.div
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm",
        className
      )}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      <div className="flex flex-col items-center space-y-4">
        <LoadingSpinner size="lg" />
        <p className="text-lg font-medium" aria-live="polite">
          {text}
        </p>
      </div>
    </motion.div>
  );
}

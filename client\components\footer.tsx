"use client"

import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ScrollAnimation, FadeInOnScroll } from "@/components/ui/scroll-animation"
import {
  Building2,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  Clock,
  Shield,
  Wifi
} from "lucide-react"

const footerLinks = {
  quickLinks: [
    { name: "Home", href: "/" },
    { name: "Rooms", href: "#rooms" },
    { name: "Facilities", href: "#facilities" },
    { name: "Testimonials", href: "#testimonials" },
  ],
  services: [
    { name: "Room Booking", href: "#contact" },
    { name: "Virtual Tour", href: "#rooms" },
    { name: "Monthly Rates", href: "#contact" },
    { name: "Student Discount", href: "#contact" },
  ],
  support: [
    { name: "Contact Us", href: "#contact" },
    { name: "FAQ", href: "#faq" },
    { name: "House Rules", href: "#rules" },
    { name: "Payment Info", href: "#payment" },
  ],
  legal: [
    { name: "Terms & Conditions", href: "/terms" },
    { name: "Privacy Policy", href: "/privacy" },
    { name: "Booking Policy", href: "/booking-policy" },
    { name: "Cancellation", href: "/cancellation" },
  ]
}

const socialLinks = [
  { name: "Facebook", icon: Facebook, href: "https://facebook.com/boardinghouse" },
  { name: "Twitter", icon: Twitter, href: "https://twitter.com/boardinghouse" },
  { name: "Instagram", icon: Instagram, href: "https://instagram.com/boardinghouse" },
  { name: "YouTube", icon: Youtube, href: "https://youtube.com/boardinghouse" },
]

const contactInfo = {
  email: "<EMAIL>",
  phone: "+62 21 1234 5678",
  whatsapp: "+62 812 3456 7890",
  address: "Jl. Sudirman No. 123, Jakarta Pusat, Indonesia 10220"
}

export function Footer() {
  return (
    <footer id="contact" className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-16">
        <ScrollAnimation animation="fadeIn" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <FadeInOnScroll delay={0.1} className="lg:col-span-1 space-y-6">
            <Link href="/" className="flex items-center space-x-2">
              <Building2 className="h-8 w-8 text-blue-400" />
              <span className="text-2xl font-bold text-white">BoardingHouse</span>
            </Link>
            <p className="text-gray-300 leading-relaxed">
              Your ideal boarding house with modern facilities, comfortable rooms, and a safe environment.
              Perfect for students and professionals.
            </p>

            {/* Key Features */}
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-sm text-gray-300">
                <Clock className="h-4 w-4 text-blue-400" />
                <span>24/7 Security & Support</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-gray-300">
                <Wifi className="h-4 w-4 text-blue-400" />
                <span>High-Speed WiFi</span>
              </div>
              <div className="flex items-center gap-3 text-sm text-gray-300">
                <Shield className="h-4 w-4 text-blue-400" />
                <span>Safe & Clean Environment</span>
              </div>
            </div>
          </FadeInOnScroll>
          </FadeInOnScroll>

          {/* Contact Info */}
          <FadeInOnScroll delay={0.2} className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Contact Info</h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3 text-sm text-gray-300">
                <Mail className="h-4 w-4 text-blue-400 mt-0.5" />
                <div>
                  <p className="font-medium text-white">Email</p>
                  <a href={`mailto:${contactInfo.email}`} className="hover:text-blue-400 transition-colors">
                    {contactInfo.email}
                  </a>
                </div>
              </div>
              <div className="flex items-start gap-3 text-sm text-gray-300">
                <Phone className="h-4 w-4 text-blue-400 mt-0.5" />
                <div>
                  <p className="font-medium text-white">Phone</p>
                  <a href={`tel:${contactInfo.phone}`} className="hover:text-blue-400 transition-colors">
                    {contactInfo.phone}
                  </a>
                </div>
              </div>
              <div className="flex items-start gap-3 text-sm text-gray-300">
                <MapPin className="h-4 w-4 text-blue-400 mt-0.5" />
                <div>
                  <p className="font-medium text-white">Address</p>
                  <p className="leading-relaxed">{contactInfo.address}</p>
                </div>
              </div>
            </div>
          </FadeInOnScroll>

          {/* Quick Links */}
          <FadeInOnScroll delay={0.3} className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Quick Links</h3>
            <ul className="space-y-3">
              {footerLinks.quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-gray-300 hover:text-blue-400 transition-colors flex items-center group"
                  >
                    <span className="group-hover:translate-x-1 transition-transform">
                      {link.name}
                    </span>
                  </Link>
                </li>
              ))}
            </ul>

            <div className="pt-4">
              <h4 className="font-semibold text-white mb-3">Services</h4>
              <ul className="space-y-2">
                {footerLinks.services.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-sm text-gray-300 hover:text-blue-400 transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </FadeInOnScroll>

          {/* Support & Legal */}
          <FadeInOnScroll delay={0.4} className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Support</h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-gray-300 hover:text-blue-400 transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>

            <div className="pt-4">
              <h4 className="font-semibold text-white mb-3">Legal</h4>
              <ul className="space-y-2">
                {footerLinks.legal.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-xs text-gray-400 hover:text-blue-400 transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Social Links */}
            <div className="pt-4">
              <h4 className="font-semibold text-white mb-3">Follow Us</h4>
              <div className="flex space-x-3">
                {socialLinks.map((social) => (
                  <motion.div
                    key={social.name}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-9 w-9 p-0 bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white transition-all duration-300"
                      asChild
                    >
                      <Link href={social.href} aria-label={social.name} target="_blank" rel="noopener noreferrer">
                        <social.icon className="h-4 w-4" />
                      </Link>
                    </Button>
                  </motion.div>
                ))}
              </div>
            </div>
          </FadeInOnScroll>
        </ScrollAnimation>

        <Separator className="my-8 bg-gray-700" />

        {/* Bottom Section */}
        <FadeInOnScroll delay={0.5}>
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <p className="text-sm text-gray-400">
              © 2024 BoardingHouse. All rights reserved.
            </p>

            <div className="flex items-center gap-6 text-sm text-gray-400">
              <span>Made with ❤️ for comfortable living</span>
              <div className="flex items-center gap-2">
                <span>Available 24/7</span>
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        </FadeInOnScroll>
      </div>
    </footer>
  )
}
